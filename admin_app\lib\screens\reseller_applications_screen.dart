import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../services/user_management_service.dart';
import '../widgets/common/custom_app_bar.dart';
import '../widgets/common/swipe_refresh_mixin.dart';

class ResellerApplicationsScreen extends StatefulWidget {
  const ResellerApplicationsScreen({super.key});

  @override
  State<ResellerApplicationsScreen> createState() => _ResellerApplicationsScreenState();
}

class _ResellerApplicationsScreenState extends State<ResellerApplicationsScreen> with SwipeRefreshMixin {
  List<UserModel> _pendingApplications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPendingApplications();
  }

  Future<void> _loadPendingApplications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print('DEBUG: Loading pending reseller applications...');
      final applications = await UserManagementService.getPendingResellerApplications();
      print('DEBUG: Found ${applications.length} pending applications');

      setState(() {
        _pendingApplications = applications;
        _isLoading = false;
      });
    } catch (e) {
      print('DEBUG ERROR: Failed to load applications - $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load applications: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  @override
  Future<void> onRefresh() async {
    await _loadPendingApplications();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Reseller Applications',
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : buildWithRefresh(
              _pendingApplications.isEmpty
                  ? _buildEmptyState()
                  : _buildApplicationsList(),
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.store_outlined,
            size: 64,
            color: AppConstants.textHintColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No pending applications',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'All reseller applications have been processed',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textHintColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _pendingApplications.length,
      itemBuilder: (context, index) {
        final application = _pendingApplications[index];
        return _buildApplicationCard(application);
      },
    );
  }

  Widget _buildApplicationCard(UserModel user) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          tilePadding: const EdgeInsets.all(AppConstants.paddingLarge),
          childrenPadding: const EdgeInsets.only(
            left: AppConstants.paddingLarge,
            right: AppConstants.paddingLarge,
            bottom: AppConstants.paddingLarge,
          ),
          title: Row(
            children: [
              CircleAvatar(
                radius: 25,
                backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                backgroundImage: user.profileImageUrl != null
                    ? NetworkImage(user.profileImageUrl!)
                    : null,
                child: user.profileImageUrl == null
                    ? Text(
                        user.displayName.isNotEmpty
                            ? user.displayName[0].toUpperCase()
                            : user.email[0].toUpperCase(),
                        style: const TextStyle(
                          color: AppConstants.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.displayNameOrUsername,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      user.email,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingSmall,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: AppConstants.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                ),
                child: Text(
                  'Pending',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.warningColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Application Details
                _buildApplicationDetails(user),

                // User Stats
                Row(
                  children: [
                    _buildStatChip('Followers', user.followerCount),
                    const SizedBox(width: AppConstants.paddingSmall),
                    _buildStatChip('Following', user.followingCount),
                    const SizedBox(width: AppConstants.paddingSmall),
                    _buildStatChip('Joined', user.formattedJoinDate),
                  ],
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _approveApplication(user),
                        icon: const Icon(Icons.check),
                        label: const Text('Approve'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConstants.successColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _rejectApplication(user),
                        icon: const Icon(Icons.close),
                        label: const Text('Reject'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppConstants.errorColor,
                          side: const BorderSide(color: AppConstants.errorColor),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildApplicationDetails(UserModel user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Enhanced Reseller Information
        if (user.resellerName != null) ...[
          _buildDetailSection('Reseller Name', user.resellerName!),
        ],

        if (user.resellerNumber != null) ...[
          _buildDetailSection('Phone Number', user.resellerNumber!),
        ],

        if (user.resellerAddress != null) ...[
          _buildDetailSection('Address', user.resellerAddress!),
        ],

        if (user.resellerReferId != null) ...[
          _buildDetailSection('Refer ID', user.resellerReferId!),
        ],

        if (user.resellerSocialMediaLink != null) ...[
          _buildDetailSection('Social Media Link', user.resellerSocialMediaLink!),
        ],

        // Document Links
        if (user.resellerNationalIdUrl != null) ...[
          _buildDocumentSection('National ID', user.resellerNationalIdUrl!),
        ],

        if (user.resellerTradeLicenseUrl != null) ...[
          _buildDocumentSection('Trade License', user.resellerTradeLicenseUrl!),
        ],

        // Referrer Information
        if (user.referrerName != null || user.referrerNumber != null || user.referrerAddress != null) ...[
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Referrer Information:',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),

          if (user.referrerName != null) ...[
            _buildDetailSection('Referrer Name', user.referrerName!),
          ],

          if (user.referrerNumber != null) ...[
            _buildDetailSection('Referrer Phone', user.referrerNumber!),
          ],

          if (user.referrerAddress != null) ...[
            _buildDetailSection('Referrer Address', user.referrerAddress!),
          ],

          if (user.referrerNationalIdUrl != null) ...[
            _buildDocumentSection('Referrer National ID', user.referrerNationalIdUrl!),
          ],
        ],

        const SizedBox(height: AppConstants.paddingMedium),
      ],
    );
  }

  Widget _buildDetailSection(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
        ],
      ),
    );
  }

  Widget _buildDocumentSection(String label, String url) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 2),
          GestureDetector(
            onTap: () => _viewDocument(url),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingSmall,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(color: AppConstants.primaryColor.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.file_present,
                    size: 16,
                    color: AppConstants.primaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'View Document',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, dynamic value) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(color: AppConstants.borderColor),
      ),
      child: Text(
        '$label: $value',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _viewDocument(String url) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Document Viewer',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppConstants.borderColor),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                    child: Image.network(
                      url,
                      fit: BoxFit.contain,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 48,
                                color: AppConstants.errorColor,
                              ),
                              SizedBox(height: AppConstants.paddingMedium),
                              Text(
                                'Failed to load document',
                                style: TextStyle(
                                  color: AppConstants.errorColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _approveApplication(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Approve Application'),
        content: Text(
          'Are you sure you want to approve ${user.displayNameOrUsername}\'s reseller application?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                print('DEBUG: Approving reseller application for user: ${user.id} (${user.displayNameOrUsername})');
                await UserManagementService.approveResellerApplication(user.id);
                print('DEBUG: Successfully approved reseller application for ${user.displayNameOrUsername}');
                _loadPendingApplications();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${user.displayNameOrUsername} approved as reseller'),
                      backgroundColor: AppConstants.successColor,
                    ),
                  );
                }
              } catch (e) {
                print('DEBUG ERROR: Failed to approve application for ${user.displayNameOrUsername} - $e');
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to approve application: $e'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.successColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Approve'),
          ),
        ],
      ),
    );
  }

  void _rejectApplication(UserModel user) {
    final reasonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Application'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Reject ${user.displayNameOrUsername}\'s reseller application?'),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Rejection Reason',
                hintText: 'Please provide a reason for rejection...',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (reasonController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please provide a rejection reason'),
                    backgroundColor: AppConstants.errorColor,
                  ),
                );
                return;
              }
              
              Navigator.of(context).pop();
              try {
                final reason = reasonController.text.trim();
                print('DEBUG: Rejecting reseller application for user: ${user.id} (${user.displayNameOrUsername}) with reason: $reason');
                await UserManagementService.rejectResellerApplication(
                  user.id,
                  reason,
                );
                print('DEBUG: Successfully rejected reseller application for ${user.displayNameOrUsername}');
                _loadPendingApplications();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${user.displayNameOrUsername}\'s application rejected'),
                      backgroundColor: AppConstants.warningColor,
                    ),
                  );
                }
              } catch (e) {
                print('DEBUG ERROR: Failed to reject application for ${user.displayNameOrUsername} - $e');
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to reject application: $e'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }
}
