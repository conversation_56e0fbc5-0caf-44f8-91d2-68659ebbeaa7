import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../models/category_model.dart';
import '../models/slider_model.dart';
import '../services/product_service.dart';
import '../services/category_service.dart';
import '../services/slider_service.dart';
import '../widgets/product_card.dart';
import '../widgets/market/banner_slider.dart';
import 'product_detail_screen.dart';
import 'search_screen.dart';

class MarketScreen extends StatefulWidget {
  const MarketScreen({super.key});

  @override
  State<MarketScreen> createState() => _MarketScreenState();
}

class _MarketScreenState extends State<MarketScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<ProductModel> _products = [];
  bool _isLoading = true;
  String _selectedCategory = 'All';
  List<CategoryModel> _categoryModels = [];
  List<String> _categories = ['All'];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      print('Loading products and categories...');

      // Test Firestore connection first
      await CategoryService.testConnection();

      final products = await ProductService.getAllProducts();
      print('Loaded ${products.length} products');

      final categoryModels = await CategoryService.getAllActiveCategories();
      print('Loaded ${categoryModels.length} categories');

      for (var cat in categoryModels) {
        print('Category: ${cat.name}, Icon: ${cat.iconName}, Image: ${cat.imageUrl}');
      }

      if (!mounted) return;

      setState(() {
        _products = products;
        _categoryModels = categoryModels;
        _categories = ['All', ...categoryModels.map((cat) => cat.name).toList()];
        _isLoading = false;
      });

      print('Categories list: $_categories');

      // If no categories loaded, use fallback
      if (categoryModels.isEmpty) {
        print('No categories loaded from Firebase, using fallback categories');
        setState(() {
          _categories = [
            'All',
            'Electronics',
            'Fashion',
            'Home & Garden',
            'Sports',
            'Books',
            'Toys',
            'Health & Beauty',
            'Automotive',
            'Food & Beverages',
            'Others',
          ];
        });
      }
    } catch (e) {
      print('Error loading data: $e');
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        // Use fallback categories on error
        _categories = [
          'All',
          'Electronics',
          'Fashion',
          'Home & Garden',
          'Sports',
          'Books',
          'Toys',
          'Health & Beauty',
          'Automotive',
          'Food & Beverages',
          'Others',
        ];
      });
      print('Using fallback categories due to error');
    }
  }

  Future<void> _searchProducts(String query) async {
    if (query.isEmpty) {
      _loadData();
      return;
    }

    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final searchResults = await ProductService.searchProducts(query);

      if (!mounted) return;

      setState(() {
        _products = searchResults;
        _isLoading = false;
      });
    } catch (e) {
      print('Error searching products: $e');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _filterByCategory(String category) async {
    if (!mounted) return;

    setState(() {
      _selectedCategory = category;
      _isLoading = true;
    });

    try {
      List<ProductModel> filteredProducts;
      if (category == 'All') {
        filteredProducts = await ProductService.getAllProducts();
      } else {
        filteredProducts = await ProductService.getProductsByCategory(category);
      }

      if (!mounted) return;

      setState(() {
        _products = filteredProducts;
        _isLoading = false;
      });
    } catch (e) {
      print('Error filtering products: $e');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Search Bar
              const SizedBox(height: 16), // Added space above the search box
              Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: AppConstants.surfaceColor,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SearchScreen(),
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                      vertical: 5, // Further reduced vertical padding for more compact design
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            Icons.search,
                            color: AppConstants.primaryColor,
                            size: 20,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'Search Product',
                            style: TextStyle(
                              color: AppConstants.textHintColor,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: AppConstants.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: IconButton(
                            padding: const EdgeInsets.all(6),
                            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                            icon: Icon(
                              Icons.tune,
                              color: AppConstants.primaryColor,
                              size: 18,
                            ),
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const SearchScreen(),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Banner Slider with conditional spacing
              _buildBannerSliderSection(),

              // Image Buttons Section
              _buildImageButtonsSection(),

              // All Buttons Section
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingMedium,
                ),
                child: Column(
                  children: [

                    // Second Row - 3 Buttons
                    Row(
                      children: [
                        Expanded(
                          child: _buildNewButton(
                            'HelpDesk',
                            Icons.help_center_outlined,
                            Colors.purple,
                            () => Navigator.pushNamed(context, '/help'),
                            showIcon: false,
                          ),
                        ),
                        const SizedBox(width: AppConstants.paddingMedium),
                        Expanded(
                          child: _buildNewButton(
                            'Support',
                            Icons.support_agent_outlined,
                            Colors.green,
                            () => Navigator.pushNamed(context, '/support'),
                            showIcon: false,
                          ),
                        ),
                        const SizedBox(width: AppConstants.paddingMedium),
                        Expanded(
                          child: _buildNewButton(
                            'Resellers',
                            Icons.business_outlined,
                            Colors.teal,
                            () => Navigator.pushNamed(context, '/resellers'),
                            showIcon: false,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Categories Grid
              const SizedBox(height: AppConstants.paddingMedium),
              _buildCategoriesGrid(),

              // All Products Section
              const SizedBox(height: AppConstants.paddingLarge),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.inventory_2,
                            color: Colors.green,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: AppConstants.paddingSmall),
                        Text(
                          'All Products',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppConstants.textPrimaryColor,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: AppConstants.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: TextButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SearchScreen(),
                            ),
                          );
                        },
                        child: Text(
                          'See All',
                          style: TextStyle(
                            color: AppConstants.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              _buildProductsGrid(),

              // See All Products Button
              _buildSeeAllProductsButton(),

              // Bottom padding for navigation gap
              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }




  Widget _buildCategoriesGrid() {
    print('Building categories grid with ${_categories.length} categories');
    print('Category models count: ${_categoryModels.length}');

    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = _selectedCategory == category;

          print('Building category $index: $category');

          // Get the category model for icon (skip for 'All')
          CategoryModel? categoryModel;
          if (category != 'All' && index > 0) {
            if (index - 1 < _categoryModels.length) {
              categoryModel = _categoryModels[index - 1]; // -1 because 'All' is at index 0
              print('Category model for $category: ${categoryModel?.iconName}');
            } else {
              print('Index out of bounds for category models: ${index - 1} >= ${_categoryModels.length}');
            }
          }

          return GestureDetector(
            onTap: () => _filterByCategory(category),
            child: Container(
              width: 80,
              margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
              child: Column(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppConstants.primaryColor
                          : AppConstants.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                    ),
                    child: categoryModel?.imageUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                            child: Image.network(
                              categoryModel!.imageUrl!,
                              width: 60,
                              height: 60,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  _getCategoryIcon(categoryModel?.iconName ?? 'category'),
                                  color: isSelected ? Colors.white : AppConstants.primaryColor,
                                  size: 32,
                                );
                              },
                            ),
                          )
                        : Icon(
                            _getCategoryIcon(categoryModel?.iconName ?? (category == 'All' ? 'grid_view' : 'category')),
                            color: isSelected ? Colors.white : AppConstants.primaryColor,
                            size: 32,
                          ),
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  Text(
                    category,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? AppConstants.primaryColor : null,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  IconData _getCategoryIcon(String iconName) {
    final Map<String, IconData> iconMap = {
      'category': Icons.category,
      'shopping_bag': Icons.shopping_bag,
      'devices': Icons.devices,
      'home': Icons.home,
      'sports': Icons.sports_soccer,
      'book': Icons.book,
      'car': Icons.directions_car,
      'restaurant': Icons.restaurant,
      'phone_android': Icons.phone_android,
      'computer': Icons.computer,
      'watch': Icons.watch,
      'headphones': Icons.headphones,
      'camera': Icons.camera_alt,
      'tv': Icons.tv,
      'tablet': Icons.tablet,
      'kitchen': Icons.kitchen,
      'bed': Icons.bed,
      'chair': Icons.chair,
      'lamp': Icons.lightbulb,
      'garden': Icons.local_florist,
      'tools': Icons.build,
      'paint': Icons.palette,
      'cleaning': Icons.cleaning_services,
      'fitness': Icons.fitness_center,
      'outdoor': Icons.terrain,
      'games': Icons.games,
      'music': Icons.music_note,
      'art': Icons.brush,
      'baby': Icons.child_care,
      'pet': Icons.pets,
      'health': Icons.health_and_safety,
      'beauty': Icons.face,
      'jewelry': Icons.diamond,
      'clothing': Icons.checkroom,
      'shoes': Icons.directions_walk,
      'bags': Icons.work,
      'accessories': Icons.watch,
      'grid_view': Icons.grid_view,
    };

    return iconMap[iconName] ?? Icons.category;
  }

  Widget _buildProductsGrid() {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(AppConstants.paddingLarge),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_products.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: AppConstants.textHintColor,
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              Text(
                'No products found',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppConstants.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.55, // Reduced to make cards taller and prevent overflow
        crossAxisSpacing: AppConstants.paddingMedium,
        mainAxisSpacing: AppConstants.paddingMedium,
      ),
      itemCount: _products.length > 6 ? 6 : _products.length, // Limit to maximum 6 products
      itemBuilder: (context, index) {
        return ProductCard(product: _products[index]);
      },
    );
  }

  Widget _buildSeeAllProductsButton() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const SearchScreen(),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.grid_view_outlined,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Text(
              'See All Products',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildSupportButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 100, // Reduced height to make it more compact
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 20,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: color,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewButton(String text, IconData icon, Color color, VoidCallback onTap, {bool showIcon = true}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (showIcon) ...[
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showComingSoonDialog(String feature) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppConstants.primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Coming Soon',
                style: TextStyle(
                  color: AppConstants.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(
            '$feature feature is coming soon! Stay tuned for updates.',
            style: TextStyle(
              color: AppConstants.textSecondaryColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'OK',
                style: TextStyle(
                  color: AppConstants.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBannerSliderSection() {
    return FutureBuilder<List<SliderModel>>(
      future: SliderService.getActiveSliders(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Column(
            children: [
              const SizedBox(height: AppConstants.paddingMedium),
              Container(
                height: 200,
                margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: AppConstants.backgroundColor,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
            ],
          );
        }

        if (snapshot.hasData && snapshot.data!.isNotEmpty) {
          return const Column(
            children: [
              SizedBox(height: AppConstants.paddingMedium),
              BannerSlider(),
            ],
          );
        }

        // No sliders available - return empty widget with no spacing
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildImageButtonsSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingXSmall,
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildImageButton(
              'Spare Parts',
              'assets/icons/spare_parts.png',
              () => Navigator.pushNamed(context, '/spare-parts'),
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: _buildImageButton(
              'Coming Soon',
              'assets/icons/coming_soon.png',
              () => Navigator.pushNamed(context, '/coming-soon'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageButton(String title, String imagePath, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          color: AppConstants.surfaceColor,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              child: Image.asset(
                imagePath,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    ),
                    child: Icon(
                      Icons.image_outlined,
                      color: AppConstants.primaryColor,
                      size: 35,
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }


}
