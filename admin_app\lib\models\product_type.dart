enum ProductType {
  spareParts('spare_parts'),
  comingSoon('coming_soon');

  const ProductType(this.value);
  final String value;

  static ProductType fromString(String value) {
    switch (value) {
      case 'spare_parts':
        return ProductType.spareParts;
      case 'coming_soon':
        return ProductType.comingSoon;
      default:
        return ProductType.spareParts; // Default to spare parts instead of regular
    }
  }

  String get displayName {
    switch (this) {
      case ProductType.spareParts:
        return 'Spare Parts';
      case ProductType.comingSoon:
        return 'Coming Soon';
    }
  }
}
