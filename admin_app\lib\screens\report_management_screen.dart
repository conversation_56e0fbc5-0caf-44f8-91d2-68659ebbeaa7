import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/report_model.dart';
import '../services/report_service.dart';
import '../providers/auth_provider.dart';
import '../constants/app_constants.dart';
import '../widgets/common/loading_widget.dart';

class ReportManagementScreen extends StatefulWidget {
  const ReportManagementScreen({super.key});

  @override
  State<ReportManagementScreen> createState() => _ReportManagementScreenState();
}

class _ReportManagementScreenState extends State<ReportManagementScreen> {
  bool _isLoading = false;
  List<ReportModel> _allReports = [];
  Map<String, int> _reportStats = {};

  @override
  void initState() {
    super.initState();
    _loadReports();
    _loadReportStats();
  }

  Future<void> _loadReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reports = await ReportService.getAllReports(limit: 100);
      setState(() {
        _allReports = reports;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading reports: ${e.toString()}'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadReportStats() async {
    try {
      final stats = await ReportService.getReportStatistics();
      setState(() {
        _reportStats = stats;
      });
    } catch (e) {
      print('Error loading report stats: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppConstants.backgroundColor,
      child: Column(
        children: [
          _buildStatsCards(),
          Expanded(
            child: _buildReportsList(),
          ),
        ],
      ),
    );
  }



  Widget _buildStatsCards() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Mobile responsive layout
          if (constraints.maxWidth < 600) {
            // Mobile: 2x2 grid
            return Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Total Reports',
                        _reportStats['total']?.toString() ?? '0',
                        Icons.report_outlined,
                        AppConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'Pending',
                        _reportStats['pending']?.toString() ?? '0',
                        Icons.pending_outlined,
                        AppConstants.warningColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Reviewed',
                        _reportStats['reviewed']?.toString() ?? '0',
                        Icons.visibility_outlined,
                        AppConstants.infoColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'Resolved',
                        _reportStats['resolved']?.toString() ?? '0',
                        Icons.check_circle_outline,
                        AppConstants.successColor,
                      ),
                    ),
                  ],
                ),
              ],
            );
          } else {
            // Desktop/Tablet: single row
            return Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Reports',
                    _reportStats['total']?.toString() ?? '0',
                    Icons.report_outlined,
                    AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Pending',
                    _reportStats['pending']?.toString() ?? '0',
                    Icons.pending_outlined,
                    AppConstants.warningColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Reviewed',
                    _reportStats['reviewed']?.toString() ?? '0',
                    Icons.visibility_outlined,
                    AppConstants.infoColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Resolved',
                    _reportStats['resolved']?.toString() ?? '0',
                    Icons.check_circle_outline,
                    AppConstants.successColor,
                  ),
                ),
              ],
            );
          }
        },
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive padding based on available width
        double padding = constraints.maxWidth < 120 ? 12.0 : 16.0;
        double iconSize = constraints.maxWidth < 120 ? 20.0 : 24.0;
        double fontSize = constraints.maxWidth < 120 ? 18.0 : 24.0;
        double titleFontSize = constraints.maxWidth < 120 ? 10.0 : 12.0;

        return Container(
          padding: EdgeInsets.all(padding),
          decoration: BoxDecoration(
            color: AppConstants.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppConstants.borderColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: iconSize),
                  SizedBox(width: constraints.maxWidth < 120 ? 4 : 8),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppConstants.textSecondaryColor,
                        fontWeight: FontWeight.w500,
                        fontSize: titleFontSize,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              SizedBox(height: constraints.maxWidth < 120 ? 4 : 8),
              Text(
                value,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontSize: fontSize,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReportsList() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_allReports.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.report_outlined,
              size: 64,
              color: AppConstants.textSecondaryColor.withOpacity(0.5),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'No reports found',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textSecondaryColor.withOpacity(0.7),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _allReports.length,
      itemBuilder: (context, index) {
        final report = _allReports[index];
        return _buildReportCard(report);
      },
    );
  }

  Widget _buildReportCard(ReportModel report) {
    return LayoutBuilder(
      builder: (context, constraints) {
        bool isMobile = constraints.maxWidth < 600;

        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          child: Padding(
            padding: EdgeInsets.all(isMobile ? AppConstants.paddingSmall : AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with status and type
                isMobile
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            _buildStatusChip(report.status),
                            const SizedBox(width: 8),
                            _buildTypeChip(report.type),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _formatDate(report.createdAt),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        _buildStatusChip(report.status),
                        const SizedBox(width: 8),
                        _buildTypeChip(report.type),
                        const Spacer(),
                        Text(
                          _formatDate(report.createdAt),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Reporter info
                Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundImage: report.reporterAvatar != null
                          ? NetworkImage(report.reporterAvatar!)
                          : null,
                      child: report.reporterAvatar == null
                          ? Text(report.reporterName.isNotEmpty ? report.reporterName[0].toUpperCase() : 'U')
                          : null,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Reported by: ${report.reporterName}',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            'Reason: ${report.reason.displayName}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppConstants.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                if (report.description != null && report.description!.isNotEmpty) ...[
                  const SizedBox(height: AppConstants.paddingSmall),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppConstants.backgroundColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppConstants.borderColor),
                    ),
                    child: Text(
                      report.description!,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],

                const SizedBox(height: AppConstants.paddingMedium),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (report.status == ReportStatus.pending) ...[
                      TextButton.icon(
                        onPressed: () => _updateReportStatus(report, ReportStatus.reviewed),
                        icon: const Icon(Icons.visibility, size: 16),
                        label: const Text('Mark as Reviewed'),
                        style: TextButton.styleFrom(
                          foregroundColor: AppConstants.infoColor,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingSmall),
                    ],
                    if (report.status != ReportStatus.resolved) ...[
                      TextButton.icon(
                        onPressed: () => _updateReportStatus(report, ReportStatus.resolved),
                        icon: const Icon(Icons.check_circle, size: 16),
                        label: const Text('Resolve'),
                        style: TextButton.styleFrom(
                          foregroundColor: AppConstants.successColor,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingSmall),
                    ],
                    TextButton.icon(
                      onPressed: () => _updateReportStatus(report, ReportStatus.dismissed),
                      icon: const Icon(Icons.close, size: 16),
                      label: const Text('Dismiss'),
                      style: TextButton.styleFrom(
                        foregroundColor: AppConstants.errorColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(ReportStatus status) {
    Color color;
    String label;

    switch (status) {
      case ReportStatus.pending:
        color = AppConstants.warningColor;
        label = 'Pending';
        break;
      case ReportStatus.reviewed:
        color = AppConstants.infoColor;
        label = 'Reviewed';
        break;
      case ReportStatus.resolved:
        color = AppConstants.successColor;
        label = 'Resolved';
        break;
      case ReportStatus.dismissed:
        color = AppConstants.errorColor;
        label = 'Dismissed';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildTypeChip(ReportType type) {
    String label;

    switch (type) {
      case ReportType.user:
        label = 'User';
        break;
      case ReportType.post:
        label = 'Post';
        break;
      case ReportType.comment:
        label = 'Comment';
        break;
      case ReportType.product:
        label = 'Product';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppConstants.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppConstants.primaryColor.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: AppConstants.primaryColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  Future<void> _updateReportStatus(ReportModel report, ReportStatus newStatus) async {
    final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
    if (currentUser == null) return;

    try {
      final success = await ReportService.updateReportStatus(
        reportId: report.id,
        status: newStatus,
        adminId: currentUser.id,
        adminName: currentUser.displayName,
      );

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Report status updated to ${newStatus.value}'),
            backgroundColor: AppConstants.successColor,
          ),
        );

        // Refresh the reports list
        _loadReports();
        _loadReportStats();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update report status'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }
}
