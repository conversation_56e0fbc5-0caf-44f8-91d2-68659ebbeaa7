import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../models/category_model.dart';
import '../models/product_type.dart';
import '../services/product_service.dart';
import '../services/image_service.dart';
import '../services/category_service.dart';
import '../providers/auth_provider.dart';
import '../widgets/product_management/product_form_widgets.dart';

class AddProductScreen extends StatefulWidget {
  const AddProductScreen({super.key});

  @override
  State<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends State<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _originalPriceController = TextEditingController();
  final _stockController = TextEditingController();
  final _whatsAppController = TextEditingController();
  final _weChatController = TextEditingController();

  List<XFile> _selectedImages = [];
  List<String> _imageUrls = [];
  List<String> _tags = [];
  String _selectedCategory = '';
  bool _isAvailable = true;
  bool _isFeatured = false;
  bool _isApproved = true;
  bool _isLoading = false;
  bool _isCategoriesLoading = true;
  ProductType _selectedProductType = ProductType.spareParts;

  List<CategoryModel> _categories = [];
  List<String> _categoryNames = [];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _stockController.dispose();
    _whatsAppController.dispose();
    _weChatController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await CategoryService.getAllActiveCategories();
      if (mounted) {
        setState(() {
          _categories = categories;
          _categoryNames = categories.map((cat) => cat.name).toList();
          _isCategoriesLoading = false;
        });
      }
    } catch (e) {
      print('Error loading categories: $e');
      if (mounted) {
        setState(() {
          _isCategoriesLoading = false;
        });
      }
    }
  }

  Future<void> _pickImages() async {
    try {
      final images = await ImageService.pickMultipleImages(
        maxImages: 10 - _selectedImages.length,
      );
      
      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images);
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error picking images: $e');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _removeImageUrl(int index) {
    setState(() {
      _imageUrls.removeAt(index);
    });
  }

  void _addImageUrl(String url) {
    setState(() {
      _imageUrls.add(url);
    });
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedImages.isEmpty && _imageUrls.isEmpty) {
      _showErrorSnackBar('Please select at least one image');
      return;
    }

    if (_selectedCategory.isEmpty) {
      _showErrorSnackBar('Please select a category');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Generate product ID
      final productId = DateTime.now().millisecondsSinceEpoch.toString();

      // Upload images and combine with URLs
      List<String> allImageUrls = [];

      // Upload file images
      if (_selectedImages.isNotEmpty) {
        final uploadedUrls = await ImageService.uploadProductImages(
          imageFiles: _selectedImages,
          productId: productId,
        );
        allImageUrls.addAll(uploadedUrls);
      }

      // Add URL images
      allImageUrls.addAll(_imageUrls);

      if (allImageUrls.isEmpty) {
        throw Exception('At least one image is required');
      }

      // Create product model
      final product = ProductModel(
        id: productId,
        sellerId: user.id,
        sellerName: user.displayName,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isNotEmpty
            ? _descriptionController.text.trim()
            : 'No description provided',
        price: _priceController.text.trim().isNotEmpty
            ? double.parse(_priceController.text)
            : 0.0, // Default price for products without price
        originalPrice: _originalPriceController.text.isNotEmpty
            ? double.parse(_originalPriceController.text)
            : null,
        category: _selectedCategory.isNotEmpty ? _selectedCategory : 'Uncategorized',
        imageUrls: allImageUrls,
        tags: _tags,
        stockQuantity: _stockController.text.trim().isNotEmpty
            ? int.parse(_stockController.text)
            : 999999, // Large number for unlimited stock
        isAvailable: _isAvailable,
        isFeatured: _isFeatured,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        sellerWhatsApp: _whatsAppController.text.trim().isNotEmpty
            ? _whatsAppController.text.trim()
            : null,
        sellerWeChat: _weChatController.text.trim().isNotEmpty
            ? _weChatController.text.trim()
            : null,
        sellerLocation: null,
        isApproved: _isApproved,
        productType: _selectedProductType,
      );

      // Save to Firestore
      final success = await ProductService.addProduct(product);

      if (success) {
        if (mounted) {
          Navigator.of(context).pop(product);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product added successfully!'),
              backgroundColor: AppConstants.successColor,
            ),
          );
        }
      } else {
        throw Exception('Failed to save product');
      }
    } catch (e) {
      _showErrorSnackBar('Error saving product: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('Add New Product'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveProduct,
              child: const Text(
                'Save',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Images Section
              ProductImagePicker(
                selectedImages: _selectedImages,
                imageUrls: _imageUrls,
                onPickImages: _pickImages,
                onRemoveImage: _removeImage,
                onRemoveImageUrl: _removeImageUrl,
                onAddImageUrl: _addImageUrl,
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Basic Information
              _buildSectionTitle('Basic Information'),
              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _nameController,
                label: 'Product Name',
                hint: 'Enter product name',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Product name is required';
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _descriptionController,
                label: 'Description (Optional)',
                hint: 'Enter product description',
                maxLines: 4,
                validator: null, // Made optional
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              _isCategoriesLoading
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(AppConstants.paddingMedium),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  : ProductCategoryDropdown(
                      selectedCategory: _selectedCategory.isNotEmpty ? _selectedCategory : null,
                      categories: _categoryNames,
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value ?? '';
                        });
                      },
                      validator: (value) {
                        // Category is optional if Coming Soon is selected
                        if (_selectedProductType == ProductType.comingSoon) {
                          return null;
                        }
                        if (value == null || value.isEmpty) {
                          return 'Please select a category';
                        }
                        return null;
                      },
                    ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Pricing Information
              _buildSectionTitle('Pricing Information'),
              const SizedBox(height: AppConstants.paddingMedium),

              Row(
                children: [
                  Expanded(
                    child: ProductFormField(
                      controller: _priceController,
                      label: 'Price (৳) - Optional',
                      hint: 'Enter price',
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          final price = double.tryParse(value);
                          if (price == null || price <= 0) {
                            return 'Enter valid price';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: ProductFormField(
                      controller: _originalPriceController,
                      label: 'Original Price (৳)',
                      hint: 'Enter original price (optional)',
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          final originalPrice = double.tryParse(value);
                          final price = double.tryParse(_priceController.text);
                          if (originalPrice == null || originalPrice <= 0) {
                            return 'Enter valid original price';
                          }
                          if (price != null && originalPrice <= price) {
                            return 'Original price must be higher than price';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _stockController,
                label: 'Stock Quantity',
                hint: 'Enter stock quantity (leave empty for unlimited)',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Tags Section
              _buildSectionTitle('Tags'),
              const SizedBox(height: AppConstants.paddingMedium),

              ProductTagsInput(
                tags: _tags,
                onTagsChanged: (tags) {
                  setState(() {
                    _tags = tags;
                  });
                },
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Contact Information
              _buildSectionTitle('Contact Information'),
              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _whatsAppController,
                label: 'WhatsApp Number',
                hint: 'Enter WhatsApp number (optional)',
                keyboardType: TextInputType.phone,
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _weChatController,
                label: 'WeChat ID',
                hint: 'Enter WeChat ID (optional)',
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Settings
              _buildSectionTitle('Settings'),
              const SizedBox(height: AppConstants.paddingMedium),

              ProductSwitchField(
                label: 'Available',
                subtitle: 'Make this product available for purchase',
                value: _isAvailable,
                onChanged: (value) {
                  setState(() {
                    _isAvailable = value;
                  });
                },
              ),

              const SizedBox(height: AppConstants.paddingSmall),

              ProductSwitchField(
                label: 'Featured',
                subtitle: 'Feature this product on homepage',
                value: _isFeatured,
                onChanged: (value) {
                  setState(() {
                    _isFeatured = value;
                  });
                },
              ),

              const SizedBox(height: AppConstants.paddingSmall),

              ProductSwitchField(
                label: 'Approved',
                subtitle: 'Approve this product for public viewing',
                value: _isApproved,
                onChanged: (value) {
                  setState(() {
                    _isApproved = value;
                  });
                },
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // Product Type Selection
              _buildProductTypeSelection(),

              const SizedBox(height: AppConstants.paddingLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: AppConstants.fontSizeLarge,
        fontWeight: FontWeight.bold,
        color: AppConstants.primaryColor,
      ),
    );
  }

  Widget _buildProductTypeSelection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.category,
                    color: AppConstants.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Product Type',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Select the type of product',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              children: [
                Expanded(
                  child: _buildToggleButton(
                    'Spare Parts',
                    _selectedProductType == ProductType.spareParts,
                    () {
                      setState(() {
                        _selectedProductType = ProductType.spareParts;
                      });
                    },
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: _buildToggleButton(
                    'Coming Soon',
                    _selectedProductType == ProductType.comingSoon,
                    () {
                      setState(() {
                        _selectedProductType = ProductType.comingSoon;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleButton(String title, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        decoration: BoxDecoration(
          color: isSelected ? AppConstants.primaryColor : AppConstants.surfaceColor,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          border: Border.all(
            color: isSelected ? AppConstants.primaryColor : AppConstants.borderColor,
            width: 2,
          ),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? Colors.white : AppConstants.textPrimaryColor,
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  String _getProductTypeDescription(ProductType type) {
    switch (type) {
      case ProductType.spareParts:
        return 'Spare parts product - appears in market, search, and spare parts page';
      case ProductType.comingSoon:
        return 'Coming soon product - only appears in coming soon page';
    }
  }
}
