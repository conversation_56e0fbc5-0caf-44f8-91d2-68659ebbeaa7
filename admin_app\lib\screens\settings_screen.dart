import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../services/settings_service.dart';
import '../widgets/common/custom_app_bar.dart';
import '../widgets/common/swipe_refresh_mixin.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with SwipeRefreshMixin {
  bool _requireUserApproval = true;
  bool _requireResellerApproval = true;
  bool _isLoading = true;

  // Contact Info Controllers
  final _whatsAppController = TextEditingController();
  final _weChatController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadApprovalSettings();
    _loadContactInfo();
  }

  @override
  void dispose() {
    _whatsAppController.dispose();
    _weChatController.dispose();
    super.dispose();
  }

  @override
  Future<void> onRefresh() async {
    await Future.wait([
      _loadApprovalSettings(),
      _loadContactInfo(),
    ]);
  }

  Future<void> _loadApprovalSettings() async {
    try {
      final settings = await SettingsService.getApprovalSettings();
      setState(() {
        _requireUserApproval = settings['require_user_approval'] ?? true;
        _requireResellerApproval = settings['require_reseller_approval'] ?? true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load settings: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _saveApprovalSettings() async {
    try {
      await SettingsService.saveApprovalSettings(
        requireUserApproval: _requireUserApproval,
        requireResellerApproval: _requireResellerApproval,
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save settings: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _loadContactInfo() async {
    try {
      final contactInfo = await SettingsService.getContactInfo();
      setState(() {
        _whatsAppController.text = contactInfo['whatsApp'] ?? '';
        _weChatController.text = contactInfo['weChat'] ?? '';
      });
    } catch (e) {
      print('Error loading contact info: $e');
    }
  }

  Future<void> _saveContactInfo() async {
    try {
      final contactInfo = {
        'whatsApp': _whatsAppController.text.trim(),
        'weChat': _weChatController.text.trim(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      final success = await SettingsService.saveContactInfo(contactInfo);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'Contact information saved successfully'
                : 'Failed to save contact information'),
            backgroundColor: success
                ? AppConstants.successColor
                : AppConstants.errorColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving contact info: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Admin Settings',
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : buildRefreshableColumn(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              children: [
                // Approval Settings Section
                _buildSectionHeader('Approval Settings'),
                _buildApprovalSettingsCard(),

                const SizedBox(height: AppConstants.paddingLarge),

                // Contact Info Section
                _buildSectionHeader('Contact info for Product Page'),
                _buildContactInfoCard(),

                const SizedBox(height: AppConstants.paddingLarge),
              ],
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppConstants.paddingMedium,
        top: AppConstants.paddingMedium,
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppConstants.textPrimaryColor,
        ),
      ),
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppConstants.textSecondaryColor,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppConstants.textSecondaryColor,
        ),
        onTap: onTap,
      ),
    );
  }



  Widget _buildApprovalSettingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.approval,
                    color: AppConstants.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Registration & Application Approvals',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Control approval requirements for new registrations',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // User Registration Approval Toggle
            Container(
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(
                  color: _requireUserApproval
                      ? AppConstants.primaryColor.withOpacity(0.3)
                      : AppConstants.textSecondaryColor.withOpacity(0.2),
                ),
              ),
              child: SwitchListTile(
                title: Text(
                  'User Registration Approval',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  _requireUserApproval
                      ? 'New user registrations require manual approval'
                      : 'New users are automatically approved',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
                value: _requireUserApproval,
                onChanged: (value) {
                  setState(() {
                    _requireUserApproval = value;
                  });
                  _saveApprovalSettings();
                },
                activeColor: AppConstants.primaryColor,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall,
                ),
              ),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Reseller Application Approval Toggle
            Container(
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(
                  color: _requireResellerApproval
                      ? AppConstants.primaryColor.withOpacity(0.3)
                      : AppConstants.textSecondaryColor.withOpacity(0.2),
                ),
              ),
              child: SwitchListTile(
                title: Text(
                  'Reseller Application Approval',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  _requireResellerApproval
                      ? 'Reseller applications require manual approval'
                      : 'Reseller applications are automatically approved',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
                value: _requireResellerApproval,
                onChanged: (value) {
                  setState(() {
                    _requireResellerApproval = value;
                  });
                  _saveApprovalSettings();
                },
                activeColor: AppConstants.primaryColor,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall,
                ),
              ),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Status Information
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: (_requireUserApproval || _requireResellerApproval)
                    ? Colors.orange[50]
                    : Colors.green[50],
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(
                  color: (_requireUserApproval || _requireResellerApproval)
                      ? Colors.orange.withOpacity(0.3)
                      : Colors.green.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    (_requireUserApproval || _requireResellerApproval)
                        ? Icons.admin_panel_settings
                        : Icons.check_circle,
                    color: (_requireUserApproval || _requireResellerApproval)
                        ? Colors.orange[700]
                        : Colors.green[700],
                    size: 20,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: Text(
                      (_requireUserApproval || _requireResellerApproval)
                          ? 'Manual approval is enabled for some registrations'
                          : 'All registrations are automatically approved',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: (_requireUserApproval || _requireResellerApproval)
                            ? Colors.orange[700]
                            : Colors.green[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.contact_phone,
                    color: AppConstants.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Global Contact Information',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Set contact info that will appear on all product pages',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // WhatsApp Field
            Container(
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(
                  color: AppConstants.textSecondaryColor.withOpacity(0.2),
                ),
              ),
              child: TextFormField(
                controller: _whatsAppController,
                decoration: InputDecoration(
                  labelText: 'WhatsApp Number',
                  hintText: '+1234567890',
                  prefixIcon: Icon(
                    Icons.chat,
                    color: const Color(0xFF25D366),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
                ),
                keyboardType: TextInputType.phone,
                // Removed auto-save on change
              ),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // WeChat Field
            Container(
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(
                  color: AppConstants.textSecondaryColor.withOpacity(0.2),
                ),
              ),
              child: TextFormField(
                controller: _weChatController,
                decoration: InputDecoration(
                  labelText: 'WeChat ID',
                  hintText: 'Your WeChat ID',
                  prefixIcon: Icon(
                    Icons.wechat,
                    color: const Color(0xFF1AAD19),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
                ),
                // Removed auto-save on change
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Save Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _saveContactInfo,
                icon: const Icon(Icons.save, size: 20),
                label: const Text(
                  'Save Contact Information',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  elevation: 2,
                ),
              ),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Info Container
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(
                  color: Colors.blue.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[700],
                    size: 20,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: Text(
                      'This contact information will be displayed on all product detail pages as WhatsApp and WeChat buttons',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
