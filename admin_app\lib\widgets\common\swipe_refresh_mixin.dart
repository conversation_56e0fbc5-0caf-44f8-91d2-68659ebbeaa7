import 'package:flutter/material.dart';

mixin SwipeRefreshMixin<T extends StatefulWidget> on State<T> {
  /// Override this method to implement the refresh logic
  Future<void> onRefresh();

  /// Wraps the given child widget with RefreshIndicator
  Widget buildWithRefresh(Widget child) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      child: child,
    );
  }

  /// Wraps a ListView with RefreshIndicator
  Widget buildRefreshableListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    EdgeInsetsGeometry? padding,
    Widget? emptyWidget,
  }) {
    if (itemCount == 0 && emptyWidget != null) {
      return RefreshIndicator(
        onRefresh: onRefresh,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.7,
            child: emptyWidget,
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: onRefresh,
      child: ListView.builder(
        controller: controller,
        padding: padding,
        itemCount: itemCount,
        itemBuilder: itemBuilder,
        physics: const AlwaysScrollableScrollPhysics(),
      ),
    );
  }

  /// Wraps a Column with RefreshIndicator
  Widget buildRefreshableColumn({
    required List<Widget> children,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    EdgeInsetsGeometry? padding,
  }) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: padding,
        child: Column(
          crossAxisAlignment: crossAxisAlignment,
          mainAxisAlignment: mainAxisAlignment,
          children: children,
        ),
      ),
    );
  }
}
