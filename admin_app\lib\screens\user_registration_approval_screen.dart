import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../services/user_management_service.dart';
import '../services/settings_service.dart';
import '../services/referral_service.dart';
import '../widgets/common/custom_app_bar.dart';
import '../widgets/common/swipe_refresh_mixin.dart';

class UserRegistrationApprovalScreen extends StatefulWidget {
  const UserRegistrationApprovalScreen({super.key});

  @override
  State<UserRegistrationApprovalScreen> createState() => _UserRegistrationApprovalScreenState();
}

class _UserRegistrationApprovalScreenState extends State<UserRegistrationApprovalScreen> with SwipeRefreshMixin {
  List<UserModel> _pendingUsers = [];
  Map<String, int> _statistics = {};
  Map<String, UserModel?> _referrerUsers = {}; // Cache for referrer user information
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final pendingUsers = await UserManagementService.getPendingRegistrationUsers();
      final statistics = await UserManagementService.getRegistrationStatistics();

      setState(() {
        _pendingUsers = pendingUsers;
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: ${e.toString()}'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  @override
  Future<void> onRefresh() async {
    await _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'User Registration Approval',
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : buildWithRefresh(
              Column(
                children: [
                  // Statistics Cards
                  _buildStatisticsCards(),

                  // Pending Users List
                  Expanded(
                    child: _pendingUsers.isEmpty
                        ? _buildEmptyState()
                        : _buildPendingUsersList(),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatisticsCards() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Pending',
              _statistics['pending'] ?? 0,
              AppConstants.warningColor,
              Icons.hourglass_empty,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: _buildStatCard(
              'Approved',
              _statistics['approved'] ?? 0,
              AppConstants.successColor,
              Icons.check_circle,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: _buildStatCard(
              'Rejected',
              _statistics['rejected'] ?? 0,
              AppConstants.errorColor,
              Icons.cancel,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, int count, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            count.toString(),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 64,
            color: AppConstants.successColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No Pending Registrations',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'All user registrations have been processed.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPendingUsersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _pendingUsers.length,
      itemBuilder: (context, index) {
        final user = _pendingUsers[index];
        return _buildUserCard(user);
      },
    );
  }

  Widget _buildUserCard(UserModel user) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          tilePadding: const EdgeInsets.all(AppConstants.paddingMedium),
          childrenPadding: const EdgeInsets.only(
            left: AppConstants.paddingMedium,
            right: AppConstants.paddingMedium,
            bottom: AppConstants.paddingMedium,
          ),
          leading: CircleAvatar(
            radius: 25,
            backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
            backgroundImage: user.profileImageUrl != null
                ? NetworkImage(user.profileImageUrl!)
                : null,
            child: user.profileImageUrl == null
                ? Text(
                    user.displayName.isNotEmpty
                        ? user.displayName[0].toUpperCase()
                        : user.username[0].toUpperCase(),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppConstants.primaryColor,
                    ),
                  )
                : null,
          ),
          title: Text(
            user.displayName.isNotEmpty ? user.displayName : user.username,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '@${user.username}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConstants.textSecondaryColor,
                ),
              ),
              Text(
                user.email,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppConstants.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 14,
                    color: AppConstants.textSecondaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Registered: ${DateFormat('MMM dd, yyyy').format(user.createdAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
          trailing: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingSmall,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              color: AppConstants.warningColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
            ),
            child: Text(
              'PENDING',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppConstants.warningColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          children: [
            _buildUserDetails(user),
          ],
        ),
      ),
    );
  }

  Widget _buildUserDetails(UserModel user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),

        // Personal Information Section
        _buildDetailSection(
          'Personal Information',
          [
            _buildDetailRow('Full Name', user.displayName.isNotEmpty ? user.displayName : 'Not provided'),
            _buildDetailRow('Username', '@${user.username}'),
            _buildDetailRow('Email', user.email),
            _buildDetailRow('Mobile', user.mobile ?? 'Not provided'),
            _buildDetailRow('Gender', user.gender ?? 'Not provided'),
            _buildDetailRow('Country', user.country ?? 'Not provided'),
            _buildDetailRow('Address', user.address ?? 'Not provided'),
          ],
        ),

        // Referrer Information Section
        _buildReferrerSection(user),

        if (user.bio != null && user.bio!.isNotEmpty) ...[
          const SizedBox(height: AppConstants.paddingMedium),
          _buildDetailSection(
            'Biography',
            [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: AppConstants.backgroundColor,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
                child: Text(
                  user.bio!,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        ],

        const SizedBox(height: AppConstants.paddingLarge),

        // Action Buttons
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _approveUser(user),
                icon: const Icon(Icons.check),
                label: const Text('Approve'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.successColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _rejectUser(user),
                icon: const Icon(Icons.close),
                label: const Text('Reject'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppConstants.errorColor,
                  side: const BorderSide(color: AppConstants.errorColor),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildReferrerSection(UserModel user) {
    return FutureBuilder<UserModel?>(
      future: _loadReferrerUser(user.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox(
            height: 50,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final referrerUser = snapshot.data;
        final hasBasicReferrerInfo = user.referrerName != null ||
                                   user.referrerNumber != null ||
                                   user.referrerAddress != null;

        if (referrerUser == null && !hasBasicReferrerInfo) {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            const SizedBox(height: AppConstants.paddingMedium),
            _buildDetailSection(
              'Referrer Information',
              [
                if (referrerUser != null) ...[
                  _buildDetailRow('Referrer Name', referrerUser.displayName.isNotEmpty ? referrerUser.displayName : 'Not provided'),
                  _buildDetailRow('Referrer Username', '@${referrerUser.username}'),
                  _buildDetailRow('Referrer Email', referrerUser.email),
                  _buildDetailRow('Referrer Mobile', referrerUser.mobile ?? 'Not provided'),
                  if (referrerUser.country != null)
                    _buildDetailRow('Referrer Country', referrerUser.country!),
                  if (referrerUser.address != null)
                    _buildDetailRow('Referrer Address', referrerUser.address!),
                ] else ...[
                  // Fallback to basic referrer info from user model
                  if (user.referrerName != null)
                    _buildDetailRow('Referrer Name', user.referrerName!),
                  if (user.referrerNumber != null)
                    _buildDetailRow('Referrer Phone', user.referrerNumber!),
                  if (user.referrerAddress != null)
                    _buildDetailRow('Referrer Address', user.referrerAddress!),
                ],
              ],
            ),
          ],
        );
      },
    );
  }

  Future<UserModel?> _loadReferrerUser(String userId) async {
    // Check cache first
    if (_referrerUsers.containsKey(userId)) {
      return _referrerUsers[userId];
    }

    try {
      final referrerUser = await ReferralService.getReferrerUser(userId);
      _referrerUsers[userId] = referrerUser;
      return referrerUser;
    } catch (e) {
      print('Error loading referrer user: $e');
      _referrerUsers[userId] = null;
      return null;
    }
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppConstants.primaryColor,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _approveUser(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Approve Registration'),
        content: Text(
          'Are you sure you want to approve ${user.displayName.isNotEmpty ? user.displayName : user.username}\'s registration?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await UserManagementService.approveUserRegistration(user.id);
                _loadData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${user.displayName.isNotEmpty ? user.displayName : user.username} approved successfully'),
                      backgroundColor: AppConstants.successColor,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error approving user: ${e.toString()}'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.successColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Approve'),
          ),
        ],
      ),
    );
  }

  void _rejectUser(UserModel user) {
    final reasonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Registration'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Reject ${user.displayName.isNotEmpty ? user.displayName : user.username}\'s registration?'),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Rejection Reason',
                hintText: 'Please provide a reason for rejection...',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final reason = reasonController.text.trim();
              if (reason.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please provide a rejection reason'),
                    backgroundColor: AppConstants.errorColor,
                  ),
                );
                return;
              }
              
              Navigator.of(context).pop();
              try {
                await UserManagementService.rejectUserRegistration(user.id, reason);
                _loadData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${user.displayName.isNotEmpty ? user.displayName : user.username}\'s registration rejected'),
                      backgroundColor: AppConstants.warningColor,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error rejecting user: ${e.toString()}'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }


}
