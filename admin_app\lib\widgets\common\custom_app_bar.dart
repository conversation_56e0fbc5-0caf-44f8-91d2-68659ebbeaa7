import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final double elevation;

  const CustomAppBar({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.backgroundColor,
    this.foregroundColor,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.elevation = 0,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          color: foregroundColor ?? Colors.black,
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: backgroundColor ?? Colors.white,
      foregroundColor: foregroundColor ?? Colors.black,
      elevation: elevation,
      centerTitle: centerTitle,
      leading: leading ?? (showBackButton 
          ? IconButton(
              icon: const Icon(
                Icons.arrow_back,
                color: Colors.black, // Always black for visibility
              ),
              onPressed: () => Navigator.of(context).pop(),
            )
          : null),
      actions: actions?.map((action) {
        // Ensure action buttons are black for visibility
        if (action is IconButton) {
          return IconButton(
            icon: action.icon is Icon 
                ? Icon((action.icon as Icon).icon, color: Colors.black)
                : action.icon,
            onPressed: action.onPressed,
            tooltip: action.tooltip,
          );
        }
        return action;
      }).toList(),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
